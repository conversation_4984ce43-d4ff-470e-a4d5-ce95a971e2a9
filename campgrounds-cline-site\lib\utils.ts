// lib/utils.ts
import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Converts a string into a URL-friendly slug.
 * Handles special characters, spacing, and trimming.
 * @param text The string to convert.
 * @returns The URL-friendly slug.
 */
export function slugify(text: string): string {
  if (!text) {
    return '';
  }
  return text
    .toString()
    .toLowerCase()
    .trim()
    .replace(/\s+/g, '-')           // Replace spaces with -
    .replace(/&/g, '-and-')         // Replace & with 'and'
    .replace(/[^\w\-]+/g, '')       // Remove all non-word chars except -
    .replace(/\-\-+/g, '-')   // Replace multiple - with single -
    .replace(/^-+/, '')       // Trim - from start of text
    .replace(/-+$/, '');      // Trim - from end of text
}

/**
 * Capitalizes the first letter of each word in a string.
 * @param text The string to capitalize.
 * @returns The capitalized string.
 */
export function capitalize(text: string): string {
  if (!text) {
    return '';
  }
  return text.replace(/-/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
}
